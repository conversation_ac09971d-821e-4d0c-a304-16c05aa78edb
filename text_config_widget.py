# -*- coding: utf-8 -*-
"""
文字配置控件
提供完整的文字样式配置功能
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                               QSlider, QComboBox, QPushButton, QCheckBox,
                               QColorDialog, QSpinBox, QGroupBox, QGridLayout)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QColor, QFont, QFontDatabase


class ColorButton(QPushButton):
    """颜色选择按钮"""
    colorChanged = Signal(QColor)
    
    def __init__(self, initial_color=QColor(0, 0, 0)):
        super().__init__()
        self.current_color = initial_color
        self.setFixedSize(40, 30)
        self.update_button_color()
        self.clicked.connect(self.choose_color)
    
    def update_button_color(self):
        """更新按钮颜色显示"""
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: rgb({self.current_color.red()}, 
                                    {self.current_color.green()}, 
                                    {self.current_color.blue()});
                border: 2px solid #333;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 2px solid #555;
            }}
        """)
    
    def choose_color(self):
        """打开颜色选择对话框"""
        color = QColorDialog.getColor(self.current_color, self, "选择颜色")
        if color.isValid():
            self.current_color = color
            self.update_button_color()
            self.colorChanged.emit(color)
    
    def get_color(self):
        """获取当前颜色"""
        return self.current_color
    
    def set_color(self, color):
        """设置颜色"""
        self.current_color = color
        self.update_button_color()


class TextConfigWidget(QWidget):
    """文字配置控件"""
    configChanged = Signal()  # 配置改变信号
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 字体组
        font_group = QGroupBox("字体设置")
        font_layout = QGridLayout(font_group)
        
        # 字体选择
        font_layout.addWidget(QLabel("字体:"), 0, 0)
        self.font_combo = QComboBox()
        self.load_system_fonts()
        font_layout.addWidget(self.font_combo, 0, 1, 1, 2)
        
        # 字体大小
        font_layout.addWidget(QLabel("大小:"), 1, 0)
        self.size_spinbox = QSpinBox()
        self.size_spinbox.setRange(8, 200)
        self.size_spinbox.setValue(24)
        font_layout.addWidget(self.size_spinbox, 1, 1)
        
        # 字体颜色
        font_layout.addWidget(QLabel("颜色:"), 2, 0)
        self.color_button = ColorButton(QColor(255, 255, 255))  # 默认白色
        font_layout.addWidget(self.color_button, 2, 1)
        
        # 透明度
        font_layout.addWidget(QLabel("透明度:"), 3, 0)
        self.alpha_slider = QSlider(Qt.Horizontal)
        self.alpha_slider.setRange(0, 100)
        self.alpha_slider.setValue(100)
        font_layout.addWidget(self.alpha_slider, 3, 1)
        self.alpha_label = QLabel("100%")
        font_layout.addWidget(self.alpha_label, 3, 2)
        
        layout.addWidget(font_group)
        
        # 描边组
        stroke_group = QGroupBox("描边设置")
        stroke_layout = QGridLayout(stroke_group)
        
        # 描边开关
        self.stroke_checkbox = QCheckBox("启用描边")
        self.stroke_checkbox.setChecked(True)  # 默认启用描边
        stroke_layout.addWidget(self.stroke_checkbox, 0, 0, 1, 3)
        
        # 描边颜色
        stroke_layout.addWidget(QLabel("描边颜色:"), 1, 0)
        self.stroke_color_button = ColorButton(QColor(0, 0, 0))  # 默认黑色描边
        stroke_layout.addWidget(self.stroke_color_button, 1, 1)
        
        # 描边粗细
        stroke_layout.addWidget(QLabel("描边粗细:"), 2, 0)
        self.stroke_width_slider = QSlider(Qt.Horizontal)
        self.stroke_width_slider.setRange(1, 10)
        self.stroke_width_slider.setValue(2)
        stroke_layout.addWidget(self.stroke_width_slider, 2, 1)
        self.stroke_width_label = QLabel("2")
        stroke_layout.addWidget(self.stroke_width_label, 2, 2)
        
        layout.addWidget(stroke_group)
        
        # 间距组
        spacing_group = QGroupBox("间距设置")
        spacing_layout = QGridLayout(spacing_group)
        
        # 字间距
        spacing_layout.addWidget(QLabel("字间距:"), 0, 0)
        self.letter_spacing_slider = QSlider(Qt.Horizontal)
        self.letter_spacing_slider.setRange(-10, 20)
        self.letter_spacing_slider.setValue(0)
        spacing_layout.addWidget(self.letter_spacing_slider, 0, 1)
        self.letter_spacing_label = QLabel("0")
        spacing_layout.addWidget(self.letter_spacing_label, 0, 2)
        
        # 行间距
        spacing_layout.addWidget(QLabel("行间距:"), 1, 0)
        self.line_spacing_slider = QSlider(Qt.Horizontal)
        self.line_spacing_slider.setRange(80, 200)
        self.line_spacing_slider.setValue(120)
        spacing_layout.addWidget(self.line_spacing_slider, 1, 1)
        self.line_spacing_label = QLabel("120%")
        spacing_layout.addWidget(self.line_spacing_label, 1, 2)
        
        layout.addWidget(spacing_group)
        
        # 预设按钮组
        preset_group = QGroupBox("预设样式")
        preset_layout = QHBoxLayout(preset_group)
        
        self.preset_white_btn = QPushButton("白字黑边")
        self.preset_black_btn = QPushButton("黑字白边")
        self.preset_red_btn = QPushButton("红字黄边")
        self.preset_blue_btn = QPushButton("蓝字白边")
        
        preset_layout.addWidget(self.preset_white_btn)
        preset_layout.addWidget(self.preset_black_btn)
        preset_layout.addWidget(self.preset_red_btn)
        preset_layout.addWidget(self.preset_blue_btn)
        
        layout.addWidget(preset_group)
    
    def load_system_fonts(self):
        """加载系统字体"""
        font_db = QFontDatabase()
        families = font_db.families()
        
        # 添加一些常用的中文字体
        preferred_fonts = [
            "Microsoft YaHei", "微软雅黑",
            "SimHei", "黑体", 
            "SimSun", "宋体",
            "KaiTi", "楷体",
            "Arial", "Times New Roman", "Calibri"
        ]
        
        # 先添加首选字体
        for font in preferred_fonts:
            if font in families:
                self.font_combo.addItem(font)
        
        # 添加分隔符
        self.font_combo.insertSeparator(self.font_combo.count())
        
        # 添加其他字体
        for family in families:
            if family not in preferred_fonts:
                self.font_combo.addItem(family)
    
    def connect_signals(self):
        """连接信号"""
        self.font_combo.currentTextChanged.connect(self.configChanged.emit)
        self.size_spinbox.valueChanged.connect(self.configChanged.emit)
        self.color_button.colorChanged.connect(self.configChanged.emit)
        self.alpha_slider.valueChanged.connect(self.on_alpha_changed)
        self.stroke_checkbox.toggled.connect(self.configChanged.emit)
        self.stroke_color_button.colorChanged.connect(self.configChanged.emit)
        self.stroke_width_slider.valueChanged.connect(self.on_stroke_width_changed)
        self.letter_spacing_slider.valueChanged.connect(self.on_letter_spacing_changed)
        self.line_spacing_slider.valueChanged.connect(self.on_line_spacing_changed)
        
        # 预设按钮
        self.preset_white_btn.clicked.connect(self.apply_white_preset)
        self.preset_black_btn.clicked.connect(self.apply_black_preset)
        self.preset_red_btn.clicked.connect(self.apply_red_preset)
        self.preset_blue_btn.clicked.connect(self.apply_blue_preset)
    
    def on_alpha_changed(self, value):
        """透明度改变"""
        self.alpha_label.setText(f"{value}%")
        self.configChanged.emit()
    
    def on_stroke_width_changed(self, value):
        """描边粗细改变"""
        self.stroke_width_label.setText(str(value))
        self.configChanged.emit()
    
    def on_letter_spacing_changed(self, value):
        """字间距改变"""
        self.letter_spacing_label.setText(str(value))
        self.configChanged.emit()
    
    def on_line_spacing_changed(self, value):
        """行间距改变"""
        self.line_spacing_label.setText(f"{value}%")
        self.configChanged.emit()
    
    def apply_white_preset(self):
        """应用白字黑边预设"""
        self.color_button.set_color(QColor(255, 255, 255))
        self.stroke_color_button.set_color(QColor(0, 0, 0))
        self.stroke_checkbox.setChecked(True)
        self.stroke_width_slider.setValue(3)
        self.configChanged.emit()
    
    def apply_black_preset(self):
        """应用黑字白边预设"""
        self.color_button.set_color(QColor(0, 0, 0))
        self.stroke_color_button.set_color(QColor(255, 255, 255))
        self.stroke_checkbox.setChecked(True)
        self.stroke_width_slider.setValue(3)
        self.configChanged.emit()
    
    def apply_red_preset(self):
        """应用红字黄边预设"""
        self.color_button.set_color(QColor(255, 0, 0))
        self.stroke_color_button.set_color(QColor(255, 255, 0))
        self.stroke_checkbox.setChecked(True)
        self.stroke_width_slider.setValue(2)
        self.configChanged.emit()
    
    def apply_blue_preset(self):
        """应用蓝字白边预设"""
        self.color_button.set_color(QColor(0, 100, 255))
        self.stroke_color_button.set_color(QColor(255, 255, 255))
        self.stroke_checkbox.setChecked(True)
        self.stroke_width_slider.setValue(2)
        self.configChanged.emit()
    
    def get_config(self):
        """获取当前配置"""
        return {
            'font_family': self.font_combo.currentText(),
            'font_size': self.size_spinbox.value(),
            'font_color': self.color_button.get_color(),
            'alpha': self.alpha_slider.value(),
            'stroke_enabled': self.stroke_checkbox.isChecked(),
            'stroke_color': self.stroke_color_button.get_color(),
            'stroke_width': self.stroke_width_slider.value(),
            'letter_spacing': self.letter_spacing_slider.value(),
            'line_spacing': self.line_spacing_slider.value()
        }
