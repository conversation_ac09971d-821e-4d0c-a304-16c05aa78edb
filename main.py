# -*- coding: utf-8 -*-
import sys
import os
import subprocess
import threading
from pathlib import Path

from PySide6.QtWidgets import (QApplication, QMainWindow, QFileDialog,
                               QGraphicsScene, QGraphicsView, QGraphicsPixmapItem,
                               QGraphicsTextItem, QMessageBox, QSlider, QVBoxLayout,
                               QHBoxLayout, QWidget, QLabel, QScrollArea)
from PySide6.QtCore import Qt, QRectF, QPointF, QThread, Signal
from PySide6.QtGui import QPixmap, QImage, QFont, QColor, QPainter, QTransform, QPen
import cv2

from ui_main_window import Ui_MainWindow
from text_config_widget import TextConfigWidget


class VideoProcessingThread(QThread):
    """视频处理线程"""
    progress_updated = Signal(str)  # 进度更新信号
    processing_finished = Signal(bool, str)  # 处理完成信号 (成功, 消息)

    def __init__(self, cmd):
        super().__init__()
        self.cmd = cmd

    def run(self):
        """在后台线程中执行FFmpeg命令"""
        try:
            self.progress_updated.emit("开始处理视频...")

            # 执行FFmpeg命令
            result = subprocess.run(self.cmd, capture_output=True, text=True, encoding='utf-8')

            if result.returncode == 0:
                self.processing_finished.emit(True, "视频处理完成！")
            else:
                error_msg = result.stderr if result.stderr else result.stdout
                self.processing_finished.emit(False, f"视频处理失败！\n错误信息：{error_msg}")
                print("FFmpeg错误:", error_msg)  # 调试输出

        except Exception as e:
            self.processing_finished.emit(False, f"处理过程中发生错误：{str(e)}")
            print("异常:", str(e))  # 调试输出


class ScalableItem(QGraphicsPixmapItem):
    """可缩放和拖拽的图形项"""
    def __init__(self, pixmap=None):
        super().__init__(pixmap)
        self.setFlag(QGraphicsPixmapItem.ItemIsMovable, True)
        self.setFlag(QGraphicsPixmapItem.ItemIsSelectable, True)
        self.setFlag(QGraphicsPixmapItem.ItemSendsGeometryChanges, True)

        self.scale_factor = 1.0
        self.min_scale = 0.1
        self.max_scale = 3.0

        # 添加缩放手柄
        self.resize_handle_size = 10
        self.is_resizing = False
        self.resize_start_pos = QPointF()
        self.resize_start_scale = 1.0

    def wheelEvent(self, event):
        """鼠标滚轮缩放"""
        if self.isSelected():
            # 获取滚轮滚动方向
            delta = event.delta()
            scale_change = 1.1 if delta > 0 else 0.9

            new_scale = self.scale_factor * scale_change
            if self.min_scale <= new_scale <= self.max_scale:
                self.scale_factor = new_scale
                self.setScale(self.scale_factor)

            event.accept()
        else:
            super().wheelEvent(event)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            # 检查是否点击在缩放手柄上
            handle_rect = self.get_resize_handle_rect()
            if handle_rect.contains(event.pos()):
                self.is_resizing = True
                self.resize_start_pos = event.pos()
                self.resize_start_scale = self.scale_factor
                event.accept()
                return

        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.is_resizing:
            # 计算缩放比例
            current_pos = event.pos()
            distance_start = (self.resize_start_pos - self.boundingRect().center()).manhattanLength()
            distance_current = (current_pos - self.boundingRect().center()).manhattanLength()

            if distance_start > 0:
                scale_ratio = distance_current / distance_start
                new_scale = self.resize_start_scale * scale_ratio

                if self.min_scale <= new_scale <= self.max_scale:
                    self.scale_factor = new_scale
                    self.setScale(self.scale_factor)

            event.accept()
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.is_resizing:
            self.is_resizing = False
            event.accept()
        else:
            super().mouseReleaseEvent(event)

    def get_resize_handle_rect(self):
        """获取缩放手柄矩形"""
        rect = self.boundingRect()
        handle_rect = QRectF(
            rect.right() - self.resize_handle_size,
            rect.bottom() - self.resize_handle_size,
            self.resize_handle_size,
            self.resize_handle_size
        )
        return handle_rect

    def paint(self, painter, option, widget=None):
        """绘制项目"""
        super().paint(painter, option, widget)

        # 如果被选中，绘制缩放手柄
        if self.isSelected():
            painter.setPen(QPen(QColor(0, 120, 255), 2))  # 蓝色边框
            painter.setBrush(QColor(0, 120, 255, 100))    # 半透明蓝色填充
            handle_rect = self.get_resize_handle_rect()
            painter.drawRect(handle_rect)


class ScalableTextItem(QGraphicsTextItem):
    """可缩放和拖拽的文字项"""
    def __init__(self, text=""):
        super().__init__(text)
        self.setFlag(QGraphicsTextItem.ItemIsMovable, True)
        self.setFlag(QGraphicsTextItem.ItemIsSelectable, True)
        self.setFlag(QGraphicsTextItem.ItemSendsGeometryChanges, True)

        # 设置默认字体和颜色
        self.base_font_size = 24
        font = QFont()
        font.setPointSize(self.base_font_size)
        self.setFont(font)
        self.setDefaultTextColor(QColor(0, 0, 0))  # 黑色字体

        self.scale_factor = 1.0
        self.min_scale = 0.1
        self.max_scale = 3.0

        # 添加缩放手柄
        self.resize_handle_size = 10
        self.is_resizing = False
        self.resize_start_pos = QPointF()
        self.resize_start_scale = 1.0

    def wheelEvent(self, event):
        """鼠标滚轮缩放"""
        if self.isSelected():
            # 获取滚轮滚动方向
            delta = event.delta()
            scale_change = 1.1 if delta > 0 else 0.9

            new_scale = self.scale_factor * scale_change
            if self.min_scale <= new_scale <= self.max_scale:
                self.scale_factor = new_scale
                # 更新字体大小而不是使用transform缩放
                font = self.font()
                font.setPointSize(int(self.base_font_size * self.scale_factor))
                self.setFont(font)

            event.accept()
        else:
            super().wheelEvent(event)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            # 检查是否点击在缩放手柄上
            handle_rect = self.get_resize_handle_rect()
            if handle_rect.contains(event.pos()):
                self.is_resizing = True
                self.resize_start_pos = event.pos()
                self.resize_start_scale = self.scale_factor
                event.accept()
                return

        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.is_resizing:
            # 计算缩放比例
            current_pos = event.pos()
            distance_start = (self.resize_start_pos - self.boundingRect().center()).manhattanLength()
            distance_current = (current_pos - self.boundingRect().center()).manhattanLength()

            if distance_start > 0:
                scale_ratio = distance_current / distance_start
                new_scale = self.resize_start_scale * scale_ratio

                if self.min_scale <= new_scale <= self.max_scale:
                    self.scale_factor = new_scale
                    # 更新字体大小
                    font = self.font()
                    font.setPointSize(int(self.base_font_size * self.scale_factor))
                    self.setFont(font)

            event.accept()
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.is_resizing:
            self.is_resizing = False
            event.accept()
        else:
            super().mouseReleaseEvent(event)

    def get_resize_handle_rect(self):
        """获取缩放手柄矩形"""
        rect = self.boundingRect()
        handle_rect = QRectF(
            rect.right() - self.resize_handle_size,
            rect.bottom() - self.resize_handle_size,
            self.resize_handle_size,
            self.resize_handle_size
        )
        return handle_rect

    def paint(self, painter, option, widget=None):
        """绘制项目"""
        super().paint(painter, option, widget)

        # 如果被选中，绘制缩放手柄
        if self.isSelected():
            painter.setPen(QPen(QColor(0, 120, 255), 2))  # 蓝色边框
            painter.setBrush(QColor(0, 120, 255, 100))    # 半透明蓝色填充
            handle_rect = self.get_resize_handle_rect()
            painter.drawRect(handle_rect)





class VideoProcessor(QMainWindow):
    def __init__(self):
        super().__init__()
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        
        # 设置窗口标题
        self.setWindowTitle("视频封边处理工具")
        
        # 初始化变量
        self.video_path = None
        self.border_path = None
        self.logo_path = None
        self.output_dir = Path("E:/000混剪文件夹/后贴")
        self.ffmpeg_path = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
        
        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化图形场景
        self.scene = QGraphicsScene()
        self.ui.graphicsView.setScene(self.scene)
        self.ui.graphicsView.setRenderHint(QPainter.Antialiasing)

        # 添加进度条控制
        self.setup_video_controls()
        
        # 图层项目
        self.video_item = None
        self.border_item = None
        self.logo_item = None
        self.text_item = None

        # 视频相关
        self.video_cap = None
        self.total_frames = 0
        self.current_frame = 0

        # 视频处理线程
        self.processing_thread = None

        # 创建文字配置面板
        self.setup_text_config_panel()

        # 连接信号槽
        self.setup_connections()

        # 设置默认文字
        self.ui.textEdit.setPlainText("请输入要添加的文字")

    def setup_text_config_panel(self):
        """设置文字配置面板"""
        # 创建文字配置控件
        self.text_config = TextConfigWidget()

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.text_config)
        scroll_area.setWidgetResizable(True)
        scroll_area.setFixedSize(350, 600)

        # 将滚动区域添加到主窗口右侧
        scroll_area.setParent(self.ui.centralwidget)
        scroll_area.setGeometry(1220, 200, 350, 600)
        scroll_area.show()

        # 连接配置改变信号
        self.text_config.configChanged.connect(self.on_text_config_changed)

    def on_text_config_changed(self):
        """文字配置改变时的处理"""
        if self.text_item and self.ui.checkBox.isChecked():
            self.update_text_style()

    def update_text_style(self):
        """更新文字样式"""
        if not self.text_item:
            return

        config = self.text_config.get_config()

        # 更新字体
        font = QFont(config['font_family'])
        font.setPointSize(config['font_size'])
        self.text_item.setFont(font)

        # 更新颜色（包含透明度）
        color = config['font_color']
        alpha = int(255 * config['alpha'] / 100)
        color.setAlpha(alpha)
        self.text_item.setDefaultTextColor(color)

    def setup_video_controls(self):
        """设置视频控制组件"""
        # 创建进度条
        self.video_slider = QSlider(Qt.Horizontal)
        self.video_slider.setMinimum(0)
        self.video_slider.setMaximum(100)
        self.video_slider.setValue(0)
        self.video_slider.setEnabled(False)  # 初始禁用

        # 设置进度条样式为蓝色
        self.video_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #999999;
                height: 8px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #B1B1B1, stop:1 #c4c4c4);
                margin: 2px 0;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #0078d4, stop:1 #106ebe);
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }
            QSlider::sub-page:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0078d4, stop:1 #106ebe);
                border: 1px solid #777;
                height: 8px;
                border-radius: 4px;
            }
        """)

        # 创建时间标签
        self.time_label = QLabel("00:00 / 00:00")
        self.time_label.setMinimumWidth(100)

        # 创建控制布局
        control_layout = QHBoxLayout()
        control_layout.addWidget(QLabel("视频进度:"))
        control_layout.addWidget(self.video_slider)
        control_layout.addWidget(self.time_label)

        # 创建控制容器
        control_widget = QWidget()
        control_widget.setLayout(control_layout)
        control_widget.setMaximumHeight(50)

        # 将控制器添加到主窗口
        # 由于我们使用的是UI文件，需要动态添加到现有布局中
        # 在graphicsView下方添加控制器
        self.control_widget = control_widget

        # 获取graphicsView的父容器并添加控制器
        graphics_view = self.ui.graphicsView
        parent_widget = graphics_view.parent()

        # 调整graphicsView的位置和大小，为控制器腾出空间
        original_geometry = graphics_view.geometry()
        new_height = original_geometry.height() - 60
        graphics_view.setGeometry(
            original_geometry.x(),
            original_geometry.y(),
            original_geometry.width(),
            new_height
        )

        # 设置控制器位置
        control_widget.setParent(parent_widget)
        control_widget.setGeometry(
            original_geometry.x(),
            original_geometry.y() + new_height + 10,
            original_geometry.width(),
            40
        )
        control_widget.show()

        # 连接信号
        self.video_slider.valueChanged.connect(self.on_slider_changed)

    def on_slider_changed(self, value):
        """进度条值改变时的处理"""
        if self.video_cap and self.total_frames > 0:
            # 计算对应的帧数
            frame_number = int((value / 100.0) * (self.total_frames - 1))
            self.show_frame(frame_number)

            # 更新时间显示
            if self.video_cap.isOpened():
                fps = self.video_cap.get(cv2.CAP_PROP_FPS)
                if fps > 0:
                    current_time = frame_number / fps
                    total_time = self.total_frames / fps

                    current_str = self.format_time(current_time)
                    total_str = self.format_time(total_time)
                    self.time_label.setText(f"{current_str} / {total_str}")

    def format_time(self, seconds):
        """格式化时间显示"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"
        
    def setup_connections(self):
        """设置信号槽连接"""
        self.ui.pushButton.clicked.connect(self.select_video)
        self.ui.pushButton_2.clicked.connect(self.select_border)
        self.ui.pushButton_3.clicked.connect(self.select_logo)
        self.ui.pushButton_4.clicked.connect(self.process_video)
        self.ui.checkBox.toggled.connect(self.toggle_text)
        self.ui.textEdit.textChanged.connect(self.update_text)
        
    def select_video(self):
        """选择视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择视频文件", "", 
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv)"
        )
        if file_path:
            self.video_path = file_path
            self.load_video_preview()
            
    def select_border(self):
        """选择封边图片"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择封边图片", "", 
            "图片文件 (*.png *.jpg *.jpeg *.bmp)"
        )
        if file_path:
            self.border_path = file_path
            self.load_border_image()
            
    def select_logo(self):
        """选择logo水印"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Logo水印", "", 
            "图片文件 (*.png *.jpg *.jpeg *.bmp)"
        )
        if file_path:
            self.logo_path = file_path
            self.load_logo_image()
            
    def load_video_preview(self):
        """加载视频预览"""
        if not self.video_path:
            return

        # 释放之前的视频捕获
        if self.video_cap:
            self.video_cap.release()

        # 创建新的视频捕获
        self.video_cap = cv2.VideoCapture(self.video_path)
        if not self.video_cap.isOpened():
            QMessageBox.warning(self, "警告", "无法打开视频文件！")
            return

        # 获取视频信息
        self.total_frames = int(self.video_cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(self.video_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(self.video_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        # 设置场景大小，保持视频原始比例
        scene_width = self.ui.graphicsView.width() - 20
        scene_height = self.ui.graphicsView.height() - 20

        # 计算等比缩放后的尺寸
        video_ratio = width / height
        view_ratio = scene_width / scene_height

        if video_ratio > view_ratio:
            # 视频更宽，以宽度为准
            self.preview_width = scene_width
            self.preview_height = int(scene_width / video_ratio)
        else:
            # 视频更高，以高度为准
            self.preview_height = scene_height
            self.preview_width = int(scene_height * video_ratio)

        self.scene.setSceneRect(0, 0, self.preview_width, self.preview_height)

        # 启用进度条
        self.video_slider.setEnabled(True)
        self.video_slider.setMaximum(100)
        self.video_slider.setValue(0)

        # 显示第一帧
        self.current_frame = 0
        self.show_frame(0)

        # 更新时间显示
        fps = self.video_cap.get(cv2.CAP_PROP_FPS)
        if fps > 0:
            total_time = self.total_frames / fps
            total_str = self.format_time(total_time)
            self.time_label.setText(f"00:00 / {total_str}")
            
    def show_frame(self, frame_number):
        """显示指定帧"""
        if not self.video_cap or not self.video_cap.isOpened():
            return

        # 设置视频位置到指定帧
        self.video_cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
        ret, frame = self.video_cap.read()

        if not ret:
            return

        try:
            # 转换OpenCV帧为QPixmap
            height, width, _ = frame.shape
            bytes_per_line = 3 * width
            q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()

            # 缩放到预览大小
            q_image = q_image.scaled(self.preview_width, self.preview_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            pixmap = QPixmap.fromImage(q_image)

            # 更新或创建视频项
            if self.video_item is None:
                self.video_item = QGraphicsPixmapItem(pixmap)
                self.video_item.setZValue(0)  # 最底层
                self.scene.addItem(self.video_item)
            else:
                self.video_item.setPixmap(pixmap)

            self.current_frame = frame_number

        except Exception as e:
            print(f"显示视频帧时出错: {e}")
            
    def load_border_image(self):
        """加载封边图片"""
        if not self.border_path:
            return

        pixmap = QPixmap(self.border_path)
        # 缩放到预览大小
        if hasattr(self, 'preview_width') and hasattr(self, 'preview_height'):
            pixmap = pixmap.scaled(self.preview_width, self.preview_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)

        if self.border_item is None:
            self.border_item = QGraphicsPixmapItem(pixmap)
            self.border_item.setZValue(2)  # 第三层
            self.scene.addItem(self.border_item)
        else:
            self.border_item.setPixmap(pixmap)
            
    def load_logo_image(self):
        """加载logo图片"""
        if not self.logo_path:
            return

        pixmap = QPixmap(self.logo_path)
        # 保持原比例，根据预览大小调整
        max_size = 100  # 默认最大尺寸
        if hasattr(self, 'preview_width'):
            max_size = min(self.preview_width // 5, 150)  # 预览宽度的1/5，最大150

        if pixmap.width() > max_size or pixmap.height() > max_size:
            pixmap = pixmap.scaled(max_size, max_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)

        if self.logo_item is None:
            self.logo_item = ScalableItem(pixmap)
            self.logo_item.setZValue(4)  # 最顶层
            # 默认位置在右上角
            default_x = getattr(self, 'preview_width', 400) - pixmap.width() - 20
            default_y = 20
            self.logo_item.setPos(default_x, default_y)
            self.scene.addItem(self.logo_item)
        else:
            self.logo_item.setPixmap(pixmap)

    def toggle_text(self, checked):
        """切换文字显示"""
        if checked:
            self.create_text_item()
        else:
            if self.text_item:
                self.scene.removeItem(self.text_item)
                self.text_item = None

    def create_text_item(self):
        """创建文字项"""
        if self.text_item is None:
            text = self.ui.textEdit.toPlainText()
            self.text_item = ScalableTextItem(text)
            self.text_item.setZValue(3)  # 第二层

            # 使用文字配置面板的设置
            self.update_text_style()

            # 默认居中位置
            center_x = getattr(self, 'preview_width', 400) // 2
            center_y = getattr(self, 'preview_height', 600) // 2
            self.text_item.setPos(center_x - 50, center_y)  # 稍微偏移以居中
            self.scene.addItem(self.text_item)

    def update_text(self):
        """更新文字内容"""
        if self.text_item and self.ui.checkBox.isChecked():
            text = self.ui.textEdit.toPlainText()
            self.text_item.setPlainText(text)

    def process_video(self):
        """处理视频"""
        if not self.video_path:
            QMessageBox.warning(self, "警告", "请先选择视频文件！")
            return

        if not self.border_path:
            QMessageBox.warning(self, "警告", "请先选择封边图片！")
            return

        # 检查FFmpeg是否存在
        if not self.ffmpeg_path.exists():
            QMessageBox.critical(self, "错误", f"FFmpeg未找到！\n请检查路径：{self.ffmpeg_path}")
            return

        # 禁用处理按钮
        self.ui.pushButton_4.setEnabled(False)
        self.ui.pushButton_4.setText("处理中...")

        # 生成输出文件名
        video_name = Path(self.video_path).stem
        output_path = self.output_dir / f"{video_name}_processed.mp4"

        # 构建FFmpeg命令
        cmd = self._build_ffmpeg_command(output_path)
        print("FFmpeg命令:", " ".join(cmd))  # 调试输出

        # 创建并启动处理线程
        self.processing_thread = VideoProcessingThread(cmd)
        self.processing_thread.progress_updated.connect(self.on_progress_updated)
        self.processing_thread.processing_finished.connect(self.on_processing_finished)
        self.processing_thread.start()

    def on_progress_updated(self, message):
        """处理进度更新"""
        print(f"处理进度: {message}")

    def on_processing_finished(self, success, message):
        """处理完成回调"""
        # 重新启用处理按钮
        self.ui.pushButton_4.setEnabled(True)
        self.ui.pushButton_4.setText("融合")

        if success:
            # 从消息中提取输出路径
            video_name = Path(self.video_path).stem
            output_path = self.output_dir / f"{video_name}_processed.mp4"
            QMessageBox.information(self, "成功", f"视频处理完成！\n输出路径：{output_path}")
        else:
            QMessageBox.critical(self, "错误", message)

    def _build_ffmpeg_command(self, output_path):
        """构建FFmpeg命令"""
        cmd = [str(self.ffmpeg_path), "-i", self.video_path]

        # 添加封边图片
        cmd.extend(["-i", self.border_path])

        # 构建基础滤镜链：视频缩放 + 封边叠加
        filter_complex = "[0:v]scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2[video];[1:v]scale=1080:1920[border];[video][border]overlay=0:0:format=auto,format=yuv420p[tmp]"

        input_count = 2
        current_output = "[tmp]"

        # 计算坐标转换比例（预览坐标 -> 实际输出坐标）
        # 输出视频尺寸是 1080x1920
        output_width = 1080
        output_height = 1920

        # 预览尺寸
        preview_width = getattr(self, 'preview_width', 1080)
        preview_height = getattr(self, 'preview_height', 1920)

        # 计算缩放比例
        scale_x = output_width / preview_width
        scale_y = output_height / preview_height

        # 添加logo（如果有）
        if self.logo_path and self.logo_item:
            cmd.extend(["-i", self.logo_path])

            # 转换坐标：预览坐标 -> 实际输出坐标
            preview_logo_x = int(self.logo_item.pos().x())
            preview_logo_y = int(self.logo_item.pos().y())
            logo_x = int(preview_logo_x * scale_x)
            logo_y = int(preview_logo_y * scale_y)

            # 获取logo在预览中的尺寸，并转换为实际输出尺寸
            preview_logo_width = int(self.logo_item.pixmap().width() * self.logo_item.scale_factor)
            preview_logo_height = int(self.logo_item.pixmap().height() * self.logo_item.scale_factor)
            actual_logo_width = int(preview_logo_width * scale_x)
            actual_logo_height = int(preview_logo_height * scale_y)

            filter_complex += f";[{input_count}:v]scale={actual_logo_width}:{actual_logo_height}[logo];{current_output}[logo]overlay={logo_x}:{logo_y}[tmp{input_count}]"
            current_output = f"[tmp{input_count}]"
            input_count += 1

            print(f"Logo坐标转换: 预览({preview_logo_x},{preview_logo_y}) -> 实际({logo_x},{logo_y})")
            print(f"Logo尺寸转换: 预览({preview_logo_width}x{preview_logo_height}) -> 实际({actual_logo_width}x{actual_logo_height})")

        # 添加文字（如果启用）- 使用新的配置系统
        if self.ui.checkBox.isChecked() and self.text_item:
            text = self.ui.textEdit.toPlainText().strip()

            if text:  # 如果有文字内容
                # 转换坐标：预览坐标 -> 实际输出坐标
                preview_text_x = int(self.text_item.pos().x())
                preview_text_y = int(self.text_item.pos().y())
                text_x = int(preview_text_x * scale_x)
                text_y = int(preview_text_y * scale_y)

                # 获取文字配置
                config = self.text_config.get_config()

                # 计算实际字体大小（考虑缩放）
                actual_font_size = int(config['font_size'] * scale_y)

                print(f"文字坐标转换: 预览({preview_text_x},{preview_text_y}) -> 实际({text_x},{text_y})")
                print(f"文字大小转换: 配置({config['font_size']}) -> 实际({actual_font_size})")

                # 简化的文字处理：只保留ASCII字符
                def simplify_text(text):
                    """简化文字，只保留安全字符"""
                    # 扩展的中文词汇映射
                    replacements = {
                        '请输入要添加的文字': 'Sample Text',
                        '不是有毒吧': 'Toxic',
                        '有毒': 'Toxic',
                        '好的': 'OK',
                        '谢谢': 'Thanks',
                        '你好': 'Hello',
                        '再见': 'Bye',
                        '是的': 'Yes',
                        '不是': 'No',
                        '可以': 'OK',
                        '没问题': 'No Problem',
                        '太好了': 'Great',
                        '厉害': 'Amazing',
                        '加油': 'Go',
                        '成功': 'Success',
                        '失败': 'Fail',
                        '开心': 'Happy',
                        '难过': 'Sad',
                        '生气': 'Angry',
                        '喜欢': 'Like',
                        '爱': 'Love',
                        '美丽': 'Beautiful',
                        '可爱': 'Cute',
                        '聪明': 'Smart',
                        '快': 'Fast',
                        '慢': 'Slow',
                        '大': 'Big',
                        '小': 'Small'
                    }

                    # 首先进行完整匹配替换
                    if text in replacements:
                        return replacements[text]

                    # 然后进行部分匹配替换
                    for chinese, english in replacements.items():
                        if chinese in text:
                            text = text.replace(chinese, english)

                    # 移除所有非ASCII字符，只保留安全字符
                    safe_chars = []
                    for char in text:
                        # 只保留ASCII字母、数字、空格和基本标点
                        if ord(char) < 128 and (char.isalnum() or char in ' .,!?-()'):
                            safe_chars.append(char)

                    result = ''.join(safe_chars).strip()

                    # 如果结果为空或太短，使用默认文字
                    if not result or len(result) < 1:
                        result = 'Text'

                    return result

                safe_text = simplify_text(text)

                # 构建高级文字滤镜，支持颜色和描边
                font_color = config['font_color']
                font_color_hex = f"0x{font_color.red():02x}{font_color.green():02x}{font_color.blue():02x}"

                # 基础文字参数
                text_params = [
                    f"text={safe_text}",
                    f"fontsize={actual_font_size}",
                    f"fontcolor={font_color_hex}",
                    f"x={text_x}",
                    f"y={text_y}"
                ]

                # 添加描边（如果启用）
                if config['stroke_enabled']:
                    stroke_color = config['stroke_color']
                    stroke_color_hex = f"0x{stroke_color.red():02x}{stroke_color.green():02x}{stroke_color.blue():02x}"
                    stroke_width = config['stroke_width']

                    text_params.extend([
                        f"borderw={stroke_width}",
                        f"bordercolor={stroke_color_hex}"
                    ])

                # 添加透明度
                if config['alpha'] < 100:
                    alpha_value = config['alpha'] / 100.0
                    text_params.append(f"alpha={alpha_value}")

                text_filter = "drawtext=" + ":".join(text_params)
                filter_complex += f";{current_output}{text_filter}[final]"
                current_output = "[final]"
                print(f"添加文字: {text} -> {safe_text}")
                print(f"文字滤镜: {text_filter}")
        else:
            print("文字功能未启用或无文字内容")

        cmd.extend(["-filter_complex", filter_complex])
        cmd.extend(["-map", current_output])
        cmd.extend(["-map", "0:a?"])  # 复制音频（如果有）
        cmd.extend(["-c:v", "libx264", "-preset", "medium", "-crf", "23"])
        cmd.extend(["-c:a", "aac", "-b:a", "128k"])
        cmd.extend(["-y", str(output_path)])  # 覆盖输出文件

        return cmd

    def closeEvent(self, event):
        """关闭事件"""
        if self.video_cap:
            self.video_cap.release()

        # 等待处理线程完成
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.quit()
            self.processing_thread.wait(3000)  # 等待最多3秒

        event.accept()


def main():
    app = QApplication(sys.argv)
    window = VideoProcessor()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
